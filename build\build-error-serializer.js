/* istanbul ignore file */
'use strict'

const FJS = require('fast-json-stringify')
const path = require('node:path')
const fs = require('node:fs')

const code = FJS({
  type: 'object',
  properties: {
    statusCode: { type: 'number' },
    code: { type: 'string' },
    error: { type: 'string' },
    message: { type: 'string' }
  }
}, { mode: 'standalone' })

const file = path.join(__dirname, '..', 'lib', 'error-serializer.js')

const moduleCode = `// This file is autogenerated by build/build-error-serializer.js, do not edit
/* c8 ignore start */
${code}
/* c8 ignore stop */
`

/* c8 ignore start */
if (require.main === module) {
  fs.writeFileSync(file, moduleCode)
  console.log(`Saved ${file} file successfully`)
} else {
  module.exports = {
    code: moduleCode
  }
}
/* c8 ignore stop */
