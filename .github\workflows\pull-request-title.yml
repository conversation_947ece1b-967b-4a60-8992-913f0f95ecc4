name: pull request title check
on:
  pull_request:
    types: [opened, edited, synchronize, reopened]

permissions:
  contents: read

jobs:
  pull-request-title-check:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
    steps:
    - uses: fastify/action-pr-title@v0
      with:
        regex: '/^(build|chore|ci|docs|feat|types|fix|perf|refactor|style|test)(?:\([^\):]*\))?!?:\s/'
        github-token: ${{ github.token }}
