name: ci

on:
  push:
    branches:
     - main
     - next
     - '*.x'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'
  workflow_dispatch:
    inputs:
      nodejs-version:
        description: 'Node.js version to use (e.g., 24.0.0-rc.1)'
        required: true
        type: string

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  dependency-review:
      name: Dependency Review
      if: github.event_name == 'pull_request'
      runs-on: ubuntu-latest
      permissions:
        contents: read
      steps:
          - name: Check out repo
            uses: actions/checkout@v4
            with:
              persist-credentials: false

          - name: Dependency review
            uses: actions/dependency-review-action@da24556b548a50705dd671f47852072ea4c105d9 # v4.7.1

  check-licenses:
    name: Check licenses
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Check licenses
        run: |
          npx license-checker --production --summary --onlyAllow="0BSD;Apache-2.0;BSD-2-Clause;BSD-3-Clause;ISC;MIT;"

  lint:
    name: Lint
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Lint code
        run: |
          npm run lint

  coverage-nix:
    needs:
      - lint
    permissions:
      contents: read
    uses: ./.github/workflows/coverage-nix.yml

  coverage-win:
    needs:
      - lint
    permissions:
      contents: read
    uses: ./.github/workflows/coverage-win.yml

  test-unit:
    needs:
      - lint
      - coverage-nix
      - coverage-win
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read

    strategy:
      matrix:
        node-version: [20, 22, 24]
        os: [macos-latest, ubuntu-latest, windows-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run tests
        run: |
          npm run unit

  # Useful for testing Release Candidates of Node.js
  test-unit-custom:
    if: github.event_name == 'workflow_dispatch'
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Custom Node.js Version
        uses: actions/setup-node@v4
        with:
          node-version: ${{ github.event.inputs.nodejs-version }}
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run tests
        run: |
          npm run unit

  test-typescript:
    needs:
      - lint
      - coverage-nix
      - coverage-win
    runs-on: 'ubuntu-latest'
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run typescript tests
        run: |
          npm run test:typescript
        env:
          NODE_OPTIONS: no-network-family-autoselection

  package:
    needs:
      - test-typescript
      - test-unit
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true
      - name: install fastify
        run: |
          npm install --ignore-scripts
      - name: install webpack stack
        run: |
          cd test/bundler/webpack && npm install --ignore-scripts
      - name: Test webpack bundle
        run: |
          cd test/bundler/webpack && npm run test
      - name: install esbuild stack
        run: |
          cd test/bundler/esbuild && npm install --ignore-scripts
      - name: Test esbuild bundle
        run: |
          cd test/bundler/esbuild && npm run test

  automerge:
    if: >
        github.event_name == 'pull_request' &&
        github.event.pull_request.user.login == 'dependabot[bot]'
    needs:
      - test-typescript
      - test-unit
      - package
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      contents: write
    steps:
      - uses: fastify/github-action-merge-dependabot@v3
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
