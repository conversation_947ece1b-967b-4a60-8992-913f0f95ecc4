name: Backport
on:
  pull_request_target:
    types:
      - closed
      - labeled

permissions:
  contents: read

jobs:
  backport:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    if: >
      github.event.pull_request.merged
      && (
        github.event.action == 'closed'
        || (
          github.event.action == 'labeled'
          && contains(github.event.label.name, 'backport')
        )
      )
    name: Backport
    steps:
      - name: Backport
        uses: tibdex/backport@9565281eda0731b1d20c4025c43339fb0a23812e # v2.0.4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
