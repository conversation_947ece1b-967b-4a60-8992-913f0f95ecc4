name: Benchmark

on:
  pull_request_target:
    types: [labeled]

permissions:
  contents: read

jobs:
  benchmark:
    if: ${{ github.event.label.name == 'benchmark' }}
    runs-on: ubuntu-latest
    permissions:
      contents: read
    outputs:
      PR-BENCH-20: ${{ steps.benchmark-pr.outputs.BENCH_RESULT20 }}
      PR-BENCH-22: ${{ steps.benchmark-pr.outputs.BENCH_RESULT22 }}
      PR-BENCH-24: ${{ steps.benchmark-pr.outputs.BENCH_RESULT24 }}
      MAIN-BENCH-20: ${{ steps.benchmark-main.outputs.BENCH_RESULT20 }}
      MAIN-BENCH-22: ${{ steps.benchmark-main.outputs.BENCH_RESULT22 }}
      MAIN-BENCH-24: ${{ steps.benchmark-main.outputs.BENCH_RESULT24 }}
    strategy:
      matrix:
        node-version: [20, 22, 24]
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
          ref: ${{github.event.pull_request.head.sha}}
          repository: ${{github.event.pull_request.head.repo.full_name}}

      - uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run benchmark
        id: benchmark-pr
        run: |
          npm run --silent benchmark > ./bench-result.md
          result=$(awk '/requests in/' ./bench-result.md)
          echo 'BENCH_RESULT${{matrix.node-version}}<<EOF' >> $GITHUB_OUTPUT
          echo "$result" >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

      # main benchmark
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
          ref: 'main'

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run benchmark
        id: benchmark-main
        run: |
          npm run --silent benchmark > ./bench-result.md
          result=$(awk '/requests in/' ./bench-result.md)
          echo 'BENCH_RESULT${{matrix.node-version}}<<EOF' >> $GITHUB_OUTPUT
          echo "$result" >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

  output-benchmark:
    needs:
      - benchmark
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Comment PR
        uses: thollander/actions-comment-pull-request@24bffb9b452ba05a4f3f77933840a6a841d1b32b # v3.0.1
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          message: |
            **Node**: 20
            **PR**: ${{ needs.benchmark.outputs.PR-BENCH-20 }}
            **MAIN**: ${{ needs.benchmark.outputs.MAIN-BENCH-20 }}

            ---

            **Node**: 22
            **PR**: ${{ needs.benchmark.outputs.PR-BENCH-22 }}
            **MAIN**: ${{ needs.benchmark.outputs.MAIN-BENCH-22 }}

            ---

            **Node**: 24
            **PR**: ${{ needs.benchmark.outputs.PR-BENCH-24 }}
            **MAIN**: ${{ needs.benchmark.outputs.MAIN-BENCH-24 }}
  remove-label:
    if: ${{ github.event.label.name == 'benchmark' }}
    needs:
      - benchmark
      - output-benchmark
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    steps:
      - name: Remove benchmark label
        uses: octokit/request-action@v2.x
        id: remove-label
        with:
          route: DELETE /repos/{repo}/issues/{issue_number}/labels/{name}
          repo: ${{ github.repository }}
          issue_number: ${{ github.event.pull_request.number }}
          name: benchmark
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
