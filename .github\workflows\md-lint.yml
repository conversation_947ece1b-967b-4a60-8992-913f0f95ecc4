name: <PERSON><PERSON> Docs

on:
  push:
    branches-ignore:
      - main
    paths:
       - "**/*.md"
  pull_request:
    branches:
      - main
    paths:
       - "**/*.md"

permissions:
  contents: read

jobs:
  build:
    name: Lint Markdown
    runs-on: ubuntu-latest
    permissions:
      contents: read

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          check-latest: true

      - name: Install Linter
        run: npm install --ignore-scripts

      - name: Add Matcher
        uses: xt0rted/markdownlint-problem-matcher@1a5fabfb577370cfdf5af944d418e4be3ea06f27 # v3.0.0

      - name: Run Linter
        run: ./node_modules/.bin/markdownlint-cli2

