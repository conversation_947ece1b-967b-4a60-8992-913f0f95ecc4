name: integration

on:
  push:
    branches:
     - main
     - next
     - 'v*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

permissions:
  contents: read

jobs:
  install-production:
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read

    strategy:
      matrix:
        node-version: [20, 22, 24]
        os: [ubuntu-latest]
        pnpm-version: [8]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          check-latest: true

      - name: Install Pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: ${{ matrix.pnpm-version }}

      - name: Install Production
        run: |
          pnpm install --ignore-scripts --prod

      - name: Run server
        run: |
          node integration/server.js &

      - name: Test
        if: ${{ success() }}
        run: |
          bash integration/test.sh
