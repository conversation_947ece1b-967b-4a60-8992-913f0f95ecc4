// This file is autogenerated by build/build-error-serializer.js, do not edit
/* c8 ignore start */

  'use strict'

  const Serializer = require('fast-json-stringify/lib/serializer')
  const serializerState = {"mode":"standalone"}
  const serializer = Serializer.restoreFromState(serializerState)

  const validator = null


  module.exports = function anonymous(validator,serializer
) {

    const JSON_STR_BEGIN_OBJECT = '{'
    const JSON_STR_END_OBJECT = '}'
    const JSON_STR_BEGIN_ARRAY = '['
    const JSON_STR_END_ARRAY = ']'
    const JSON_STR_COMMA = ','
    const JSON_STR_COLONS = ':'
    const JSON_STR_QUOTE = '"'
    const JSON_STR_EMPTY_OBJECT = JSON_STR_BEGIN_OBJECT + JSON_STR_END_OBJECT
    const JSON_STR_EMPTY_ARRAY = JSON_STR_BEGIN_ARRAY + JSON_STR_END_ARRAY
    const JSON_STR_EMPTY_STRING = JSON_STR_QUOTE + JSON_STR_QUOTE
    const JSON_STR_NULL = 'null'
  
    
  
    // #
    function anonymous0 (input) {
      const obj = (input && typeof input.toJSON === 'function')
    ? input.toJSON()
    : input
  
      if (obj === null) return JSON_STR_EMPTY_OBJECT

      let value
let json = JSON_STR_BEGIN_OBJECT
let addComma = false

      value = obj["statusCode"]
      if (value !== undefined) {
        !addComma && (addComma = true) || (json += JSON_STR_COMMA)
        json += "\"statusCode\":"
        json += serializer.asNumber(value)
      }

      value = obj["code"]
      if (value !== undefined) {
        !addComma && (addComma = true) || (json += JSON_STR_COMMA)
        json += "\"code\":"
        
        if (typeof value !== 'string') {
          if (value === null) {
            json += JSON_STR_EMPTY_STRING
          } else if (value instanceof Date) {
            json += JSON_STR_QUOTE + value.toISOString() + JSON_STR_QUOTE
          } else if (value instanceof RegExp) {
            json += serializer.asString(value.source)
          } else {
            json += serializer.asString(value.toString())
          }
        } else {
          json += serializer.asString(value)
        }
        
      }

      value = obj["error"]
      if (value !== undefined) {
        !addComma && (addComma = true) || (json += JSON_STR_COMMA)
        json += "\"error\":"
        
        if (typeof value !== 'string') {
          if (value === null) {
            json += JSON_STR_EMPTY_STRING
          } else if (value instanceof Date) {
            json += JSON_STR_QUOTE + value.toISOString() + JSON_STR_QUOTE
          } else if (value instanceof RegExp) {
            json += serializer.asString(value.source)
          } else {
            json += serializer.asString(value.toString())
          }
        } else {
          json += serializer.asString(value)
        }
        
      }

      value = obj["message"]
      if (value !== undefined) {
        !addComma && (addComma = true) || (json += JSON_STR_COMMA)
        json += "\"message\":"
        
        if (typeof value !== 'string') {
          if (value === null) {
            json += JSON_STR_EMPTY_STRING
          } else if (value instanceof Date) {
            json += JSON_STR_QUOTE + value.toISOString() + JSON_STR_QUOTE
          } else if (value instanceof RegExp) {
            json += serializer.asString(value.source)
          } else {
            json += serializer.asString(value.toString())
          }
        } else {
          json += serializer.asString(value)
        }
        
      }

    return json + JSON_STR_END_OBJECT
  
    }
  
    const main = anonymous0
    return main
    
}(validator, serializer)
/* c8 ignore stop */
