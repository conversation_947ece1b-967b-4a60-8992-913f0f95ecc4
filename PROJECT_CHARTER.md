# Fastify Charter

The Fastify project aims to build a fast and low-overhead web framework for
Node.js.


## Section 0: Guiding Principles

The Fastify project is part of the [OpenJS Foundation][openjs foundation]. It
operates transparently, openly, collaboratively, and ethically. Project
proposals, timelines, and status must not merely be open, but also easily
visible to outsiders.


## Section 1: Scope

Fastify is a web framework highly focused on providing the best developer
experience with the least overhead and a plugin architecture.

### 1.1: In-scope

+ Develop a web framework for Node.js with a focus on developer experience,
  performance, and extensibility.
+ Plugin Architecture
+ Support web protocols
+ Official plugins for common user requirements
+ Documentation:
  + Project (policies, processes, and releases)
  + Guides and Tutorials
  + Framework API
  + Website
+ Write easier APIs for developers
+ Tools:
  + CI services
  + Bots to improve overall efficiency
  + Releases
+ Support:
  + Community
  + Users's issues and questions
+ Contributors's pull request review

### 1.2: Out-of-Scope

+ Support versions of Node.js at EOL (end of life) stage
+ Support serverless architecture
+ Contributions that violate the [Code of Conduct](CODE_OF_CONDUCT.md)


## Section 2: Relationship with OpenJS Foundation CPC.

Technical leadership for the projects within the [OpenJS Foundation][openjs
foundation] is delegated to the projects through their project charters by the
[OpenJS Foundation Cross-Project Council](https://openjsf.org/about/governance/)
(CPC). In the case of the Fastify project, it is delegated to the [Fastify
Collaborators](README.md#team). The OpenJS Foundation's business leadership is
the Board of Directors (the "Board").

This Fastify Charter reflects a carefully constructed balanced role for the
Collaborators and the CPC in the governance of the OpenJS Foundation. The
charter amendment process is for the Fastify Collaborators to propose change
using a majority of the full Fastify Organization, the proposed changes
being subject to review and approval by the CPC. The CPC may additionally make
amendments to the Collaborators charter at any time, though the CPC will not
interfere with day-to-day discussions, votes, or meetings of the Fastify
Organization.


### 2.1 Other Formal Project Relationships

Section Intentionally Left Blank


## Section 3: Fastify Governing Body

Fastify is governed by its [maintainers](README.md#team). See [how it is
structured](GOVERNANCE.md) for more information.


## Section 4: Roles & Responsibilities

The roles and responsibilities of Fastify's maintainers are described in [the
project organization](GOVERNANCE.md).

### Section 4.1 Project Operations & Management

Section Intentionally Left Blank

### Section 4.2: Decision-making, Voting, and/or Elections

**Decision-making**

Fastify's features can be discussed in GitHub issues and/or projects. Consensus
on a discussion is reached when there is no objection by any collaborators.

When there is no consensus, Lead Maintainers will have the final say on the
topic.

**Voting, and/or Elections**

These processes are described in the [GOVERNANCE](GOVERNANCE.md) document.

### Section 4.3: Other Project Roles

Section Intentionally Left Blank

## Section 5: Definitions

+ *Contributors*: contribute code or other artifacts, but do not have the right
  to commit to the code base. Contributors work with the project’s Collaborators
  to have code committed to the code base. Contributors should rarely be
  encumbered by the Fastify Collaborators and never by the CPC or OpenJS
  Foundation Board.

+ *Collaborators*: contribute code and other artifacts, have the right to commit
  to the code base, and release plugin projects. Collaborators follow the
  [CONTRIBUTING](CONTRIBUTING.md) guidelines to manage the project. A
  Collaborator could be encumbered by other Fastify Collaborators and never by
  the CPC or OpenJS Foundation Board.

+ *Lead Maintainers*: founders of the project, contribute code and other
  artifacts, have the right to commit to the code base and release the project.
  Lead Maintainers follow the [CONTRIBUTING](CONTRIBUTING.md) guidelines to
  manage the project. A Lead Maintainer will be encumbered by the Fastify
  Collaborators and by the CPC or OpenJS Foundation Board.

[openjs foundation]: https://openjsf.org
