name: Internal Links Check

on:
  pull_request:
    paths:
      - 'docs/**'
      - '*.md'

permissions:
  contents: read

jobs:
  linkChecker:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Check out repo
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      # It will be possible to check only for the links in the changed files
      # See: https://github.com/lycheeverse/lychee-action/issues/17
      - name: Link Checker
        id: lychee
        uses: lycheeverse/lychee-action@82202e5e9c2f4ef1a55a3d02563e1cb6041e5332 # v2.4.1
        with:
          fail: true
          # As external links behavior is not predictable, we check only internal links
          # to ensure consistency.
          # See: https://github.com/lycheeverse/lychee-action/issues/17#issuecomment-1162586751
          args: --offline --verbose --no-progress './**/*.md'
        env:
          GITHUB_TOKEN: ${{secrets.GITHUB_TOKEN}}
