name: package-manager-ci

on:
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  pnpm:
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read

    strategy:
      matrix:
        # Maintenance and active LTS
        node-version: [20, 22, 24]
        os: [ubuntu-latest]
        pnpm-version: [8]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          check-latest: true

      - name: Install with pnpm
        uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda # v4.1.0
        with:
          version: ${{ matrix.pnpm-version }}

      - run: pnpm install --ignore-scripts

      - name: Run tests
        run: |
          pnpm run test:ci
        env:
          NODE_OPTIONS: no-network-family-autoselection

  yarn:
    runs-on: ${{ matrix.os }}
    permissions:
      contents: read

    strategy:
      matrix:
        # Maintenance and active LTS
        node-version: [20, 22, 24]
        os: [ubuntu-latest]

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          check-latest: true

      - name: Install with yarn
        run: |
          curl -o- -L https://yarnpkg.com/install.sh | bash
          yarn install --ignore-engines --ignore-scripts

      - run: yarn

      - name: Run tests
        run: |
          yarn run test:ci
        env:
          NODE_OPTIONS: no-network-family-autoselection
