name: Code Coverage (*nix)

on:
  workflow_call:

permissions:
  contents: read

jobs:
  check-coverage:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'npm'
          cache-dependency-path: package.json
          check-latest: true

      - name: Install
        run: |
          npm install --ignore-scripts

      # Here, we verify the coverage thresholds so that this workflow can pass
      # or fail and stop further workflows if this one fails.
      - name: Verify coverage meets thresholds
        run: |
          npm run coverage:ci-check-coverage
