name: ci Alternative Runtimes

on:
  push:
    branches:
     - main
     - next
     - 'v*'
    paths-ignore:
      - 'docs/**'
      - '*.md'
  pull_request:
    paths-ignore:
      - 'docs/**'
      - '*.md'

# This allows a subsequently queued workflow run to interrupt previous runs
concurrency:
  group: "${{ github.workflow }} @ ${{ github.event.pull_request.head.label || github.head_ref || github.ref }}"
  cancel-in-progress: true

permissions:
  contents: read

jobs:
  test-unit:
    runs-on: ${{ matrix.os }}
    continue-on-error: true
    permissions:
      contents: read
    strategy:
      matrix:
        node-version: [20]
        os: [macos-latest, ubuntu-latest, windows-latest]
        include:
          - runtime: nsolid
            node-version: 20
            nsolid-version: 5
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: nodesource/setup-nsolid@1ca68d2589d3d56ecd3881dfe6ffa87eeda9c939 # v1.0.1
        if: ${{ matrix.runtime == 'nsolid' }}
        with:
          node-version: ${{ matrix.node-version }}
          nsolid-version: ${{ matrix.nsolid-version }}

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run tests
        run: |
          npm run unit

  test-typescript:
    runs-on: 'ubuntu-latest'
    permissions:
      contents: read

    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false

      - uses: nodesource/setup-nsolid@1ca68d2589d3d56ecd3881dfe6ffa87eeda9c939 # v1.0.1
        with:
          node-version: 20
          nsolid-version: 5

      - name: Install
        run: |
          npm install --ignore-scripts

      - name: Run typescript tests
        run: |
          npm run test:typescript
        env:
          NODE_OPTIONS: no-network-family-autoselection

  package:
    needs:
      - test-typescript
      - test-unit
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - uses: actions/checkout@v4
        with:
          persist-credentials: false
      - uses: nodesource/setup-nsolid@1ca68d2589d3d56ecd3881dfe6ffa87eeda9c939 # v1.0.1
        with:
          nsolid-version: 5
      - name: install fastify
        run: |
          npm install --ignore-scripts
      - name: install webpack stack
        run: |
          cd test/bundler/webpack && npm install
      - name: Test webpack bundle
        run: |
          cd test/bundler/webpack && npm run test
      - name: install esbuild stack
        run: |
          cd test/bundler/esbuild && npm install
      - name: Test esbuild bundle
        run: |
          cd test/bundler/esbuild && npm run test
